# logoutput: /var/log/socks.log
internal: 10.0.0.3 port = 10800
external: 10.0.0.3
clientmethod: none
socksmethod: none
user.privileged: root
user.notprivileged: nobody

client pass {
        from: 0/0 to: 0/0
        log: error connect disconnect
}

socks pass {
        from: 0/0 to: 0/0
        command: bind connect udpassociate
        log: error connect disconnect
        socksmethod: none
}

socks pass {
        from: 0.0.0.0/0 to: 0.0.0.0/0
        command: bindreply udpreply
}

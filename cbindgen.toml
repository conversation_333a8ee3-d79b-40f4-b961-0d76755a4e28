language = "C"
cpp_compat = true

[export]
include = [
    "tun2proxy_run_with_cli",
    "tun2proxy_with_fd_run",
    "tun2proxy_with_name_run",
    "tun2proxy_stop",
    "tun2proxy_set_log_callback",
    "tun2proxy_set_traffic_status_callback",
]
exclude = [
    "Java_com_github_shadowsocks_bg_Tun2proxy_run",
    "Java_com_github_shadowsocks_bg_Tun2proxy_stop",
    "UdpFlag",
]

[export.rename]
"ArgVerbosity" = "Tun2proxyVerbosity"
"ArgDns" = "Tun2proxyDns"
"TrafficStatus" = "Tun2proxyTrafficStatus"

[enum]
prefix_with_name = true

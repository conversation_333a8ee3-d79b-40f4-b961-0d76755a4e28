name: Publish Docker Images

on:
  push:
    tags: [ 'v*.*.*' ]

# Defines two custom environment variables for the workflow. These are used for the Container registry domain, and a name for the Docker image that this workflow builds.
env:
  REGISTRY: ghcr.io
  # This also contains the owner, i.e. tun2proxy/tun2proxy.
  IMAGE_PATH: ${{ github.repository }}
  IMAGE_NAME: ${{ github.event.repository.name }}
  DEFAULT_OS: scratch

# There is a single job in this workflow. It's configured to run on the latest available version of Ubuntu.
jobs:
  build-and-push-image:
    name: Build and push Docker image
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        os: [ 'scratch', 'ubuntu', 'alpine' ]
    # Sets the permissions granted to the `GITHUB_TOKEN` for the actions in this job.
    permissions:
      contents: read
      packages: write
      #
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

        # Add support for more platforms with QEMU (optional)
        # https://github.com/docker/setup-qemu-action
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Uses the `docker/login-action` action to log in to the Container registry registry using the account and password that will publish the packages. Once published, the packages are scoped to the account defined here.
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # This step uses [docker/metadata-action](https://github.com/docker/metadata-action#about) to extract tags and labels that will be applied to the specified image. The `id` "meta" allows the output of this step to be referenced in a subsequent step. The `images` value provides the base name for the tags and labels.
      - name: Extract metadata (tags, labels) for Docker Image
        id: meta
        uses: docker/metadata-action@v5
        with:
          # We publish the images with an OS-suffix.
          # The image based on a default OS is also published without a suffix.
          images: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_PATH }}-${{ matrix.os }}
            ${{ env.DEFAULT_OS == matrix.os && format('{0}/{1}', env.REGISTRY, env.IMAGE_PATH) || '' }}

      # This step uses the `docker/build-push-action` action to build the image, based on your repository's `Dockerfile`. If the build succeeds, it pushes the image to GitHub Packages.
      # It uses the `context` parameter to define the build's context as the set of files located in the specified path. For more information, see "[Usage](https://github.com/docker/build-push-action#usage)" in the README of the `docker/build-push-action` repository.
      # It uses the `tags` and `labels` parameters to tag and label the image with the output from the "meta" step.
      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          context: .
          file: Dockerfile
          target: ${{ env.IMAGE_NAME }}-${{ matrix.os }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

on:
  pull_request_review:
    types: [submitted]
  push:
  workflow_dispatch:
  pull_request_target:
    types: [labeled]

name: Integration Tests

jobs:
  proxy_tests:
    name: Proxy Tests
    runs-on: ubuntu-latest
    if: (github.event_name != 'pull_request' || contains(github.event.pull_request.labels.*.name, 'safe to test')) && github.actor != 'dependabot[bot]' && github.actor != 'github-actions[bot]'
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - name: Populate .env
        env:
          DOTENV: ${{ secrets.DOTENV }}
        run: |
          echo "$DOTENV" > tests/.env
          ln -s tests/.env

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'
      
      - name: Create virtual environment
        run: python -m venv venv
      
      - name: Activate virtual environment and install dependencies
        run: |
          source venv/bin/activate
          pip install -r tests/requirements.txt
      
      - name: Build project
        run: cargo build --release

      - name: Run tests
        run: |
          source venv/bin/activate
          python tests/tests.py

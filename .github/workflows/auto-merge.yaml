name: Dependabot Auto Merge

on:
  pull_request_target:
    types: [labeled]

jobs:
  auto:
    if: github.actor == 'dependabot[bot]'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Auto approve pull request, then squash and merge
        uses: ahmadnassri/action-dependabot-auto-merge@v2
        with:
          # target: minor
          # here `PAT_REPO_ADMIN` is a user's passkey provided by github.
          github-token: ${{ secrets.PAT_REPO_ADMIN }}
